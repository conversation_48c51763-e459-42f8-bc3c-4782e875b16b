'use client'

import React, {
  useState, useEffect,
} from 'react';
import * as fabric from 'fabric';
import { 
  ArrowDownToLine, ArrowUpFromLine,
} from 'lucide-react';
import { Button } from '@/common/components/atoms';

interface CanvasWithV6Methods extends fabric.Canvas {
  insertAt: (index: number, ...objects: fabric.Object[]) => number;
}

interface CanvasToolbarProps {
  onClose: () => void;
  onSaveDesign: () => void;
  canvas?: fabric.Canvas | null;
}

export const CanvasToolbar = ({
  onClose,
  onSaveDesign,
  canvas,
}: CanvasToolbarProps) => {
  const [selectedObject, setSelectedObject] = useState<fabric.Object | null>(null);
  const [canMoveUp, setCanMoveUp] = useState(false);
  const [canMoveDown, setCanMoveDown] = useState(false);

  useEffect(() => {
    if (!canvas) {
      return;
    }

    const updateSelection = () => {
      const activeObject = canvas.getActiveObject();
      setSelectedObject(activeObject || null);

      if (activeObject) {
        const objects = canvas.getObjects();
        const currentIndex = objects.indexOf(activeObject);
        setCanMoveUp(currentIndex < objects.length - 1);
        setCanMoveDown(currentIndex > 0);
      } else {
        setCanMoveUp(false);
        setCanMoveDown(false);
      }
    };

    canvas.on('selection:created', updateSelection);
    canvas.on('selection:updated', updateSelection);
    canvas.on('selection:cleared', updateSelection);
    canvas.on('object:added', updateSelection);
    canvas.on('object:removed', updateSelection);

    return () => {
      canvas.off('selection:created', updateSelection);
      canvas.off('selection:updated', updateSelection);
      canvas.off('selection:cleared', updateSelection);
      canvas.off('object:added', updateSelection);
      canvas.off('object:removed', updateSelection);
    };
  }, [canvas]);

  const moveLayerUp = () => {
    if (!canvas || !selectedObject) {
      return;
    }

    const objects = canvas.getObjects();
    const currentIndex = objects.indexOf(selectedObject);

    if (currentIndex < objects.length - 1) {
      canvas.remove(selectedObject);
      (canvas as CanvasWithV6Methods).insertAt(currentIndex + 1, selectedObject);
      canvas.renderAll();
    }

    const newObjects = canvas.getObjects();
    const newIndex = newObjects.indexOf(selectedObject);
    setCanMoveUp(newIndex < newObjects.length - 1);
    setCanMoveDown(newIndex > 0);
  };

  const moveLayerDown = () => {
    if (!canvas || !selectedObject) {
      return;
    }

    const objects = canvas.getObjects();
    const currentIndex = objects.indexOf(selectedObject);

    if (currentIndex > 0) {
      canvas.remove(selectedObject);
      (canvas as CanvasWithV6Methods).insertAt(currentIndex - 1, selectedObject);
      canvas.renderAll();
    }

    const newObjects = canvas.getObjects();
    const newIndex = newObjects.indexOf(selectedObject);
    setCanMoveUp(newIndex < newObjects.length - 1);
    setCanMoveDown(newIndex > 0);
  };

  return (
    <div className="bg-eerie-black border-b border-neutral-700 px-2 md:px-8 py-2 md:py-3 flex items-center justify-between relative">
      <div className="flex items-center gap-1 md:gap-2">
        {selectedObject && (
          <>
            <Button
              onClick={moveLayerUp}
              disabled={!canMoveUp}
              variant="outline"
              size="sm"
              title="Bring to Front"
            >
              <ArrowUpFromLine className="w-4 h-4" />
            </Button>
            <Button
              onClick={moveLayerDown}
              disabled={!canMoveDown}
              variant="outline"
              size="sm"
              title="Send Back"
            >
              <ArrowDownToLine className="w-4 h-4" />
            </Button>
          </>
        )}
      </div>
      <div className="absolute left-1/2 transform -translate-x-1/2 text-center">
        <h1 className="text-white font-semibold text-sm md:text-lg">Image Editor</h1>
      </div>
      <div className="flex items-center gap-1 md:gap-2">
        <Button
          variant="gradient"
          size="sm"
          onClick={onSaveDesign}
        >
          Save
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={onClose}
        >
          Close
        </Button>
      </div>
    </div>
  );
};
